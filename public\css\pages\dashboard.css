/* Dashboard Page Specific Styles */

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.dashboard-header h1 {
    color: #333;
    font-size: 28px;
    margin: 0;
}

.dashboard-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Application Toggle Styles */
.application-toggle-container {
    display: flex;
    align-items: center;
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
    cursor: pointer;
}

.toggle-text {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.toggle-switch-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toggle-input {
    display: none;
}

.toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    transition: background 0.3s;
    cursor: pointer;
}

.toggle-input:checked + .toggle-switch {
    background: #28a745;
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-input:checked + .toggle-switch .toggle-slider {
    transform: translateX(26px);
}

.toggle-status {
    font-size: 12px;
    font-weight: 500;
    min-width: 40px;
}

.toggle-input:checked ~ .toggle-status {
    color: #28a745;
}

.toggle-input:not(:checked) ~ .toggle-status {
    color: #dc3545;
}

.refresh-btn {
    background: #1e5631;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #164023;
    transform: translateY(-1px);
}

.refresh-btn i {
    transition: transform 0.3s ease;
}

.refresh-btn:hover i {
    transform: rotate(180deg);
}

.date {
    color: #666;
    font-size: 14px;
}



/* View All Links */
.view-all {
    color: #1e5631;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.view-all:hover {
    text-decoration: underline;
}

/* Applications Table */
.applications-table {
    width: 100%;
    border-collapse: collapse;
}

.applications-table th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-size: 14px;
    color: #555;
    font-weight: 600;
    border-bottom: 1px solid #ddd;
}

.applications-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    color: #333;
}

.applications-table tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status.pending {
    background-color: #fff8e1;
    color: #f57f17;
}

.status.review {
    background-color: #e3f2fd;
    color: #1565c0;
}

.status.approved {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status.rejected {
    background-color: #ffebee;
    color: #c62828;
}

/* Action Buttons */
.action-btn {
    display: inline-block;
    padding: 5px 10px;
    background-color: #1e5631;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-size: 12px;
}

.action-btn:hover {
    background-color: #164023;
}

.action-btn.delete {
    background-color: #dc3545;
    margin-left: 5px;
}

.action-btn.delete:hover {
    background-color: #c82333;
}



/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.charts-section.line-chart-section {
    grid-template-columns: 1fr;
    margin-top: 20px;
}

.chart-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.chart-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: #052f11;
    border-radius: 2px;
}

.chart-download-btn {
    background: none;
    border: none;
    color: #052f11;
    cursor: pointer;
    font-size: 18px;
    padding: 4px;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-download-btn:hover {
    color: #041f0c;
}

.chart-download-btn i {
    font-size: 18px;
}

.chart-canvas {
    position: relative;
    height: 300px;
}

/* Responsive chart layout */
@media (max-width: 1200px) {
    .charts-section {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 1024px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .chart-canvas {
        height: 250px;
    }

    .chart-container {
        padding: 15px;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 15px;
    }

    .chart-title {
        font-size: 16px;
        margin: 0;
    }

    .chart-download-btn {
        padding: 4px;
        font-size: 16px;
        align-self: flex-end;
    }
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.action-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.action-card:hover {
    transform: translateY(-5px);
    text-decoration: none;
    color: inherit;
}

.action-icon {
    font-size: 32px;
    color: #1e5631;
    margin-bottom: 10px;
}

.action-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.action-description {
    font-size: 12px;
    color: #666;
}

/* Student Categories */
.student-categories {
    margin-bottom: 30px;
}

.category-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 12px 20px;
    border: 2px solid #1e5631;
    background-color: white;
    color: #1e5631;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background-color: #1e5631;
    color: white;
}

.student-table-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    background-color: #f8f9fa;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.table-header h3 {
    margin: 0;
    color: #333;
}

.table-actions {
    display: flex;
    gap: 10px;
}

/* Archive Tables */
.archive-table,
.students-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.archive-table th,
.archive-table td,
.students-table th,
.students-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.archive-table th,
.students-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

/* Responsive Design */
@media (max-width: 1024px) {
}

@media (max-width: 768px) {
    .admin-container {
        grid-template-columns: 1fr;
    }

    .admin-sidebar {
        margin-bottom: 30px;
    }

    .category-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        padding: 10px 15px;
        font-size: 14px;
    }

    .table-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .table-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
}

/* GWA Requirements Styling */
.gwa-requirements {
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

.gwa-requirements .form-help-text {
    margin: 0;
    color: #495057;
    line-height: 1.4;
}

/* ===================================
   INLINE STYLES MOVED FROM ADMIN DASHBOARD BLADE FILE
   =================================== */

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 0;
    max-width: 650px;
    width: 95%;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    margin: 20px;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #1e5631;
    color: white;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: white;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 30px 35px;
}

.modal-footer {
    padding: 25px 35px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* Button Styles */
.btn-primary, .btn-secondary, .btn-warning, .btn-danger {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
    font-size: 14px;
}

.btn-primary {
    background: #1e5631;
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    font-weight: 700;
    cursor: pointer;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(30, 86, 49, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    font-weight: 700;
    cursor: pointer;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Form Group Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e8f0fe;
    border-radius: 12px;
    font-size: 15px;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    font-weight: 500;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #1e5631;
    box-shadow: 0 0 0 3px rgba(30, 86, 49, 0.12);
}

/* Settings Section Styles */
.settings-section {
    margin-bottom: 32px;
    background: #ffffff;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f3f4;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h4 {
    color: #1e5631;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-section h4::before {
    content: '';
    width: 4px;
    height: 24px;
    background: #1e5631;
    border-radius: 2px;
}

.form-help-text {
    margin-top: 18px;
    padding: 16px 18px;
    border-radius: 10px;
    font-size: 13px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-help-text.info {
    background: #e8f4fd;
    border-left: 4px solid #052f11;
    color: #052f11;
    border: 1px solid rgba(5, 47, 17, 0.2);
}

.form-help-text.warning {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

/* Enhanced Toggle Switch Styles */
.toggle-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 64px;
    height: 32px;
    flex-shrink: 0;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e74c3c;
    border-radius: 32px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 3px;
    top: 3px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

input:checked + .toggle-slider {
    background: #052f11;
}

input:checked + .toggle-slider:before {
    transform: translateX(32px);
}

/* Application Control Section */
.application-control-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    background: #f8fffe;
    border-radius: 16px;
    border: 2px solid #e8f5e8;
    margin-bottom: 0;
    box-shadow: 0 4px 16px rgba(30, 86, 49, 0.08);
    position: relative;
}

.application-control-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #1e5631;
}

.application-control-info {
    flex: 1;
    padding-right: 24px;
}

.application-control-info h5 {
    margin: 0 0 8px 0;
    font-weight: 700;
    color: #1e5631;
    font-size: 17px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.application-control-info h5::before {
    content: '🎯';
    font-size: 16px;
}

.application-control-info p {
    margin: 0;
    color: #5a6c57;
    font-size: 14px;
    line-height: 1.6;
    font-weight: 500;
}

.toggle-control {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(30, 86, 49, 0.1);
    gap: 12px;
}

.toggle-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-text {
    font-weight: 700;
    font-size: 14px;
    min-width: 60px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Advanced Operations */
.advanced-operations {
    display: flex;
    gap: 16px;
    margin-bottom: 0;
}

.advanced-operations button {
    flex: 1;
    padding: 16px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 700;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-warning {
    background: #f39c12;
    color: white;
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

.btn-danger {
    background: #e74c3c;
    color: white;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Confirmation Dialog Styles */
.modal-content.confirm-dialog {
    max-width: 350px !important;
    width: 350px !important;
    height: 350px !important;
    min-height: 350px !important;
}

.confirm-dialog .modal-body {
    text-align: center;
    padding: 20px;
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.confirm-dialog .confirm-icon {
    font-size: 40px;
    color: #ffc107;
    margin-bottom: 15px;
    display: block;
}

.confirm-dialog .confirm-message {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 20px;
    text-align: center;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.confirm-dialog .confirm-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.confirm-dialog .confirm-buttons button {
    min-width: 80px;
    padding: 10px 16px;
    font-weight: 500;
    font-size: 14px;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

/* Specific button colors for confirmation dialog */
.confirm-dialog .btn-primary {
    background-color: #1e5631;
    color: white;
}

.confirm-dialog .btn-primary:hover {
    background-color: #2d7a47;
}

.confirm-dialog .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.confirm-dialog .btn-secondary:hover {
    background-color: #5a6268;
}

/* Smaller header for confirmation dialog */
.confirm-dialog .modal-header {
    padding: 15px 20px;
    height: 60px;
    text-align: center;
    justify-content: center;
    position: relative;
}

.confirm-dialog .modal-header h3 {
    font-size: 16px;
    margin: 0 auto;
    flex-grow: 1;
    text-align: center;
}

.confirm-dialog .close-btn {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

.confirm-dialog .close-btn {
    font-size: 18px;
    padding: 0;
    width: 20px;
    height: 20px;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #ffffff;
    border-radius: 20px;
    padding: 0;
    max-width: 700px;
    width: 95%;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    margin: 20px;
}

.modal-header {
    padding: 24px 32px;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #1e5631;
    color: white;
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: white;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-header h3::before {
    content: '⚙️';
    font-size: 18px;
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    font-size: 20px;
    cursor: pointer;
    color: white;
    padding: 0;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-body {
    padding: 32px 40px;
    background: #ffffff;
}

.modal-footer {
    padding: 24px 40px;
    border-top: 1px solid #e8f0fe;
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    background: #f8fffe;
    border-radius: 0 0 20px 20px;
}
