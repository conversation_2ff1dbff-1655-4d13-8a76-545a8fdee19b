@extends('layouts.admin')

@section('title', 'Grantee Management')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/pages/students.css') }}">
    <style>
        /* Grade Disqualification Notification Styles */
        .main-screen-grade-disqualification-notification {
            background: #f8d7da;
            border: 2px solid #dc3545;
            border-radius: 12px;
            margin: 20px auto;
            max-width: 900px;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.2);
            animation: slideDownBounce 0.5s ease-out;
            position: relative;
            z-index: 1000;
        }

        .notification-content {
            display: flex;
            align-items: flex-start;
            padding: 20px;
            gap: 15px;
        }

        .notification-icon {
            flex-shrink: 0;
            width: 50px;
            height: 50px;
            background: #dc3545;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }

        .notification-icon i {
            color: white;
            font-size: 1.5rem;
        }

        .notification-text {
            flex: 1;
            color: #721c24;
            font-size: 1rem;
            line-height: 1.5;
        }

        .notification-text strong {
            color: #dc3545;
            font-weight: 700;
            font-size: 1.1rem;
        }

        .warning-note {
            display: block;
            margin-top: 8px;
            font-size: 0.85rem;
            color: #856404;
            font-style: italic;
            background: rgba(255, 255, 255, 0.7);
            padding: 6px 10px;
            border-radius: 4px;
            border-left: 3px solid #dc3545;
        }

        .notification-close {
            flex-shrink: 0;
            background: none;
            border: none;
            color: #dc3545;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .notification-close:hover {
            background: rgba(220, 53, 69, 0.1);
            transform: scale(1.1);
        }

        /* Disqualified Grade Input Styles */
        input.grade-disqualified {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
            background-color: #fff5f5 !important;
            animation: gradeShake 0.5s ease-in-out;
        }

        @keyframes gradeShake {

            0%,
            100% {
                transform: translateX(0);
            }

            25% {
                transform: translateX(-5px);
            }

            75% {
                transform: translateX(5px);
            }
        }

        @keyframes slideDownBounce {
            0% {
                opacity: 0;
                transform: translateY(-30px) scale(0.9);
            }

            60% {
                opacity: 1;
                transform: translateY(5px) scale(1.02);
            }

            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.6;
            }
        }

        /* Responsive Design for Grade Disqualification Notification */
        @media (max-width: 768px) {
            .main-screen-grade-disqualification-notification {
                margin: 15px 10px;
                max-width: calc(100% - 20px);
            }

            .notification-content {
                padding: 15px;
                gap: 10px;
            }

            .notification-icon {
                width: 40px;
                height: 40px;
            }

            .notification-icon i {
                font-size: 1.2rem;
            }

            .notification-text {
                font-size: 0.9rem;
            }

            .notification-text strong {
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .main-screen-grade-disqualification-notification {
                margin: 10px 5px;
            }

            .notification-content {
                padding: 12px;
                gap: 8px;
            }

            .notification-text {
                font-size: 0.85rem;
                line-height: 1.4;
            }

            .notification-close {
                width: 30px;
                height: 30px;
                font-size: 1rem;
            }
        }
    </style>
@endpush

@section('breadcrumbs')
    <x-breadcrumb :items="[['title' => 'Grantee', 'icon' => 'fas fa-users']]" />
@endsection

@section('content')
    <div class="dashboard-header">
        <h1>
            @if (isset($scholarshipName))
                {{ $scholarshipName }} Grantees
            @else
                Grantee Management
            @endif
        </h1>
        <div class="date">{{ date('F d, Y') }}</div>
    </div>



    <!-- Grantee Categories -->
    <div class="student-categories">
        <div class="category-tabs">
            <button class="tab-btn {{ !isset($scholarshipTypeFilter) ? 'active' : '' }}"
                onclick="showStudentCategory('all', this)">All Grantees</button>
            <button
                class="tab-btn {{ isset($scholarshipTypeFilter) && $scholarshipTypeFilter === 'government' ? 'active' : '' }}"
                onclick="showStudentCategory('government', this)">Government Grantees</button>
            <button
                class="tab-btn {{ isset($scholarshipTypeFilter) && $scholarshipTypeFilter === 'academic' ? 'active' : '' }}"
                onclick="showStudentCategory('academic', this)">Academic Grantees</button>
            <button
                class="tab-btn {{ isset($scholarshipTypeFilter) && $scholarshipTypeFilter === 'employees' ? 'active' : '' }}"
                onclick="showStudentCategory('employees', this)">Employee Grantees</button>
            <button
                class="tab-btn {{ isset($scholarshipTypeFilter) && $scholarshipTypeFilter === 'alumni' ? 'active' : '' }}"
                onclick="showStudentCategory('alumni', this)">Alumni Grantees</button>
        </div>
    </div>

    <!-- Grantee Table -->
    <div class="student-table-container">
        <div class="table-header">
            <h3 id="categoryTitle">
                @if (isset($scholarshipTypeFilter))
                    @switch($scholarshipTypeFilter)
                        @case('government')
                            Government Grantees
                        @break

                        @case('academic')
                            Academic Grantees
                        @break

                        @case('employees')
                            Employee Grantees
                        @break

                        @case('alumni')
                            Alumni Grantees
                        @break

                        @default
                            All Grantees
                    @endswitch
                @else
                    All Grantees
                @endif
            </h3>
            <div class="table-actions" id="tableActions">
                <button class="btn-secondary" id="importBtn" onclick="triggerFileImport()"
                    style="display: {{ isset($scholarshipTypeFilter) ? 'none' : 'block' }};">
                    <i class="fas fa-upload"></i> Import
                </button>

            </div>
        </div>
        <table class="students-table">
            <thead>
                <tr id="tableHeader">
                    @if (isset($scholarshipTypeFilter))
                        @if ($scholarshipTypeFilter == 'government')
                            <th>Student ID</th>
                            <th>Full Name</th>
                            <th>Course/Strand</th>
                            <th>Benefactor Type</th>
                            <th>Semester</th>
                            <th>Academic Year</th>
                            <th>Status</th>
                            <th>Actions</th>
                        @elseif($scholarshipTypeFilter == 'academic')
                            <th>Student ID</th>
                            <th>Full Name</th>
                            <th>Course</th>
                            <th>GWA</th>
                            <th>Classification</th>
                            <th>Semester</th>
                            <th>Academic Year</th>
                            <th>Status</th>
                            <th>Actions</th>
                        @elseif($scholarshipTypeFilter == 'employees')
                            <th>Student ID</th>
                            <th>Full Name</th>
                            <th>Employee Name</th>
                            <th>Relationship</th>
                            <th>Semester</th>
                            <th>Academic Year</th>
                            <th>Status</th>
                            <th>Actions</th>
                        @elseif($scholarshipTypeFilter == 'alumni')
                            <th>Student ID</th>
                            <th>Full Name</th>
                            <th>Course</th>
                            <th>Scholarship Name</th>
                            <th>Semester</th>
                            <th>Academic Year</th>
                            <th>Status</th>
                            <th>Actions</th>
                        @endif
                    @else
                        <!-- Default view for all grantees -->
                        <th>Student ID</th>
                        <th>Name</th>
                        <th>Course/Strand</th>
                        <th>Benefactor Type</th>
                        <th>Semester</th>
                        <th>Academic Year</th>
                        <th>Status</th>
                        <th>Actions</th>
                    @endif
                </tr>
            </thead>
            <tbody id="studentsTableBody">
                @forelse($students as $student)
                    <tr data-type="{{ strtolower(str_replace(' ', '', $student['scholarship_type'])) }}">
                        @if (isset($scholarshipTypeFilter))
                            @if ($scholarshipTypeFilter == 'government')
                                <td>{{ $student['id'] }}</td>
                                <td>{{ $student['name'] }}</td>
                                <td>
                                    @if (!empty($student['strand']))
                                        {{ $student['strand'] }}
                                    @elseif (!empty($student['course']))
                                        {{ $student['course'] }}
                                    @else
                                        N/A
                                    @endif
                                </td>
                                <td>
                                    <span class="benefactor-badge">
                                        {{ $student['government_benefactor_type'] ?? 'N/A' }}
                                    </span>
                                </td>
                                <td><span
                                        class="semester-badge">{{ $student['current_semester'] ?? $currentSemester }}</span>
                                </td>
                                <td><span
                                        class="academic-year-badge">{{ $student['current_academic_year'] ?? $currentAcademicYear }}</span>
                                </td>
                                <td>
                                    <span class="status-badge {{ strtolower($student['status'] ?? 'active') }}">
                                        {{ $student['status'] ?? 'Active' }}
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn edit"
                                        onclick="editStudent('{{ $student['application_id'] }}', '{{ $student['id'] }}')"
                                        title="Edit Grantee">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            @elseif($scholarshipTypeFilter == 'academic')
                                <td>{{ $student['id'] }}</td>
                                <td>{{ $student['name'] }}</td>
                                <td>{{ $student['course'] }}</td>
                                <td>{{ $student['gwa'] }}</td>
                                <td>
                                    <span class="classification-badge">
                                        @if ($student['gwa'] >= 1.0 && $student['gwa'] <= 1.25)
                                            PL (President's Lister)
                                        @elseif($student['gwa'] >= 1.26 && $student['gwa'] <= 1.74)
                                            DL (Dean's Lister)
                                        @else
                                            N/A
                                        @endif
                                    </span>
                                </td>
                                <td><span
                                        class="semester-badge">{{ $student['current_semester'] ?? $currentSemester }}</span>
                                </td>
                                <td><span
                                        class="academic-year-badge">{{ $student['current_academic_year'] ?? $currentAcademicYear }}</span>
                                </td>
                                <td>
                                    <span class="status-badge {{ strtolower($student['status'] ?? 'active') }}">
                                        {{ $student['status'] ?? 'Active' }}
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn edit"
                                        onclick="editStudent('{{ $student['application_id'] }}', '{{ $student['id'] }}')"
                                        title="Edit Grantee">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            @elseif($scholarshipTypeFilter == 'employees')
                                <td>{{ $student['id'] }}</td>
                                <td>{{ $student['name'] }}</td>
                                <td>{{ $student['employee_name'] ?? 'N/A' }}</td>
                                <td>{{ $student['employee_relationship'] ?? 'N/A' }}</td>
                                <td><span
                                        class="semester-badge">{{ $student['current_semester'] ?? $currentSemester }}</span>
                                </td>
                                <td><span
                                        class="academic-year-badge">{{ $student['current_academic_year'] ?? $currentAcademicYear }}</span>
                                </td>
                                <td>
                                    <span class="status-badge {{ strtolower($student['status'] ?? 'active') }}">
                                        {{ $student['status'] ?? 'Active' }}
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn edit"
                                        onclick="editStudent('{{ $student['application_id'] }}', '{{ $student['id'] }}')"
                                        title="Edit Grantee">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            @elseif($scholarshipTypeFilter == 'alumni')
                                <td>{{ $student['id'] }}</td>
                                <td>{{ $student['name'] }}</td>
                                <td>{{ $student['course'] }}</td>
                                <td>{{ $student['scholarship_name'] ?? 'N/A' }}</td>
                                <td><span
                                        class="semester-badge">{{ $student['current_semester'] ?? $currentSemester }}</span>
                                </td>
                                <td><span
                                        class="academic-year-badge">{{ $student['current_academic_year'] ?? $currentAcademicYear }}</span>
                                </td>
                                <td>
                                    <span class="status-badge {{ strtolower($student['status'] ?? 'active') }}">
                                        {{ $student['status'] ?? 'Active' }}
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn edit"
                                        onclick="editStudent('{{ $student['application_id'] }}', '{{ $student['id'] }}')"
                                        title="Edit Grantee">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            @endif
                        @else
                            <!-- Default view for all grantees -->
                            <td>{{ $student['id'] }}</td>
                            <td>{{ $student['name'] }}</td>
                            <td>
                                @if (!empty($student['strand']))
                                    {{ $student['strand'] }}
                                @elseif (!empty($student['course']))
                                    {{ $student['course'] }}
                                @else
                                    N/A
                                @endif
                            </td>
                            <td>{{ $student['scholarship_type'] }}</td>
                            <td><span class="semester-badge">{{ $student['current_semester'] ?? $currentSemester }}</span>
                            </td>
                            <td><span
                                    class="academic-year-badge">{{ $student['current_academic_year'] ?? $currentAcademicYear }}</span>
                            </td>
                            <td>
                                <span class="status-badge {{ strtolower($student['status'] ?? 'active') }}">
                                    {{ $student['status'] ?? 'Active' }}
                                </span>
                            </td>
                            <td>
                                <button class="action-btn edit"
                                    onclick="editStudent('{{ $student['application_id'] }}', '{{ $student['id'] }}')"
                                    title="Edit Grantee">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </td>
                        @endif
                    </tr>
                @empty
                    <tr>
                        <td colspan="{{ isset($scholarshipTypeFilter) && in_array($scholarshipTypeFilter, ['government', 'academic']) ? '9' : (isset($scholarshipTypeFilter) && $scholarshipTypeFilter == 'employees' ? '8' : (isset($scholarshipTypeFilter) && $scholarshipTypeFilter == 'alumni' ? '8' : '8')) }}"
                            class="empty-state">
                            No grantees found. Grantees will appear here once benefactor applications are approved.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
@endsection

<!-- Edit Grantee Modal -->
<div id="editStudentModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Edit Grantee Information</h2>
            <span class="close" onclick="closeEditModal()">&times;</span>
        </div>
        <form id="editStudentForm" onsubmit="saveStudentChanges(event)">
            <div class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editStudentId">Grantee ID</label>
                        <input type="text" id="editStudentId" name="student_id" readonly>
                    </div>
                    <div class="form-group">
                        <label for="editStudentName">Full Name</label>
                        <input type="text" id="editStudentName" name="name" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="editStudentStatus">Status</label>
                        <select id="editStudentStatus" name="status" required>
                            <option value="Active">Active</option>
                            <option value="Inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="form-row" id="remarksRow" style="display: none;">
                    <div class="form-group">
                        <label for="editStudentRemarks">Remarks (Required if status is Inactive)</label>
                        <select id="editStudentRemarks" name="notes" required>
                            <option value="">Select Reason</option>
                            <option value="Transferred">Transferred</option>
                            <option value="Graduated">Graduated</option>
                            <option value="Failure to Meet Academic Requirements">Failure to Meet Academic Requirements
                            </option>
                        </select>
                    </div>
                </div>
                <input type="hidden" id="editApplicationId" name="application_id">
                <input type="hidden" id="editGranteeId" name="grantee_id">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeEditModal()">Cancel</button>
                <button type="submit" class="btn-primary">Save Changes</button>
            </div>
        </form>
    </div>
</div>



<!-- Hidden file input for import -->
<input type="file" id="hiddenFileInput" accept=".xlsx,.xls,.csv" style="display: none;"
    onchange="handleFileSelection(event)">

<!-- Import Confirmation Modal (shown after file selection) -->
<div id="importConfirmationModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Import Grantees</h2>
            <span class="close" onclick="closeImportConfirmationModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="form-section">
                <p>Selected file: <strong id="selectedFileName"></strong></p>
                <div class="form-row">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="updateExistingCheckbox">
                            Update existing grantees if Grantee ID already exists
                        </label>
                    </div>
                </div>
                <div class="import-instructions">
                    <div class="instruction-header">
                        <i class="fas fa-info-circle"></i>
                        <h4>Import Instructions</h4>
                    </div>
                    <div class="instruction-content">
                        <p>Excel file should contain the following columns:</p>
                        <div class="column-list">
                            <span class="required-column">Student ID</span>
                            <span class="required-column">First Name</span>
                            <span class="required-column">Last Name</span>
                            <span class="required-column">Scholarship Type</span>
                            <span class="optional-column">Middle Name</span>
                            <span class="optional-column">Email</span>
                            <span class="optional-column">Course</span>
                            <span class="optional-column">Department</span>
                            <span class="optional-column">Year Level</span>
                            <span class="optional-column">GWA</span>
                        </div>
                        <div class="import-notes">
                            <div class="note-item">
                                <i class="fas fa-asterisk"></i>
                                <span>Required fields: Student ID, First Name, Last Name, Scholarship Type</span>
                            </div>
                            <div class="note-item">
                                <i class="fas fa-table"></i>
                                <span>First row should contain column headers</span>
                            </div>
                            <div class="note-item">
                                <i class="fas fa-users"></i>
                                <span>Students will be automatically grouped by their scholarship type from the file</span>
                            </div>
                            <div class="note-item">
                                <i class="fas fa-list"></i>
                                <span>Valid scholarship types: government, academic, employees, alumni</span>
                            </div>
                        </div>
                        <div class="template-download">
                            <a href="/admin/download-student-template" target="_blank" class="download-link">
                                <i class="fas fa-download"></i>
                                Download Template
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeImportConfirmationModal()">Cancel</button>
            <button type="button" class="btn-primary" onclick="proceedWithImport()">Import Students</button>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        // Add event listener when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the duplicate event listener - button already has onclick attribute

            // Test function to manually populate courses
            window.testCoursePopulation = function() {
                console.log('Testing course population...');
                const courseSelect = document.getElementById('course');
                if (courseSelect) {
                    courseSelect.innerHTML = '<option value="">Select Course</option>';
                    const testCourses = [
                        'Bachelor of Science in Information Technology',
                        'Bachelor of Science in Computer Science',
                        'Bachelor of Science in Computer Engineering'
                    ];
                    testCourses.forEach(course => {
                        const option = document.createElement('option');
                        option.value = course;
                        option.textContent = course;
                        courseSelect.appendChild(option);
                    });
                    courseSelect.disabled = false;
                    console.log('Test courses added successfully');
                } else {
                    console.error('Course select not found');
                }
            };

            // Force populate courses for current department selection
            window.forcePopulateCourses = function() {
                console.log('Force populating courses...');

                // Check for Academic form
                const deptSelect = document.getElementById('department');
                const courseSelect = document.getElementById('course');

                if (deptSelect && courseSelect && deptSelect.value) {
                    console.log('Found Academic form, department:', deptSelect.value);
                    const siteCourses = [
                        'Bachelor of Science in Information Technology',
                        'Bachelor of Science in Computer Science',
                        'Bachelor of Science in Computer Engineering',
                        'Bachelor of Library and Information Science',
                        'Bachelor of Science in Civil Engineering'
                    ];

                    courseSelect.innerHTML = '<option value="">Select Course</option>';
                    siteCourses.forEach(course => {
                        const option = document.createElement('option');
                        option.value = course;
                        option.textContent = course;
                        courseSelect.appendChild(option);
                    });
                    courseSelect.disabled = false;
                    console.log('Courses populated for Academic form');
                    return;
                }

                // Check for Government form
                const govDeptSelect = document.getElementById('governmentDepartment');
                const govCourseSelect = document.getElementById('governmentCourse');

                if (govDeptSelect && govCourseSelect && govDeptSelect.value) {
                    console.log('Found Government form, department:', govDeptSelect.value);
                    const siteCourses = [
                        'Bachelor of Science in Information Technology',
                        'Bachelor of Science in Computer Science',
                        'Bachelor of Science in Computer Engineering',
                        'Bachelor of Library and Information Science',
                        'Bachelor of Science in Civil Engineering'
                    ];

                    govCourseSelect.innerHTML = '<option value="">Select Course</option>';
                    siteCourses.forEach(course => {
                        const option = document.createElement('option');
                        option.value = course;
                        option.textContent = course;
                        govCourseSelect.appendChild(option);
                    });
                    govCourseSelect.disabled = false;
                    console.log('Courses populated for Government form');
                    return;
                }

                console.error('No department selected or form not found');
            };

            // Add direct event listeners for department dropdowns
            setTimeout(() => {
                // For Academic scholarship
                const deptSelect = document.getElementById('department');
                if (deptSelect) {
                    deptSelect.addEventListener('change', function() {
                        console.log('Department changed via event listener:', this.value);
                        loadCoursesByDepartment();
                    });
                }

                // For Government scholarship
                const governmentDeptSelect = document.getElementById('governmentDepartment');
                if (governmentDeptSelect) {
                    governmentDeptSelect.addEventListener('change', function() {
                        console.log('Government Department changed via event listener:', this
                            .value);
                        loadGovernmentCoursesByDepartment();
                    });
                }
            }, 1000);

            // Initialize page with correct active tab based on filter
            @if (isset($scholarshipTypeFilter))
                // If there's a scholarship type filter, activate the corresponding tab
                const filterType = '{{ $scholarshipTypeFilter }}';
                console.log('Initializing with filter type:', filterType);
                activateTabByType(filterType);

                // Also ensure the correct tab is visually highlighted
                setTimeout(() => {
                    const activeTab = document.querySelector('.tab-btn.active');
                    if (activeTab) {
                        console.log('Active tab found:', activeTab.textContent);
                        // Add a subtle visual indicator that this tab is filtered
                        activeTab.classList.add('viewing');
                        activeTab.style.boxShadow = '0 0 15px rgba(5, 47, 17, 0.4)';
                    }
                }, 100);
            @endif
        });

        // Function to activate tab based on scholarship type
        function activateTabByType(scholarshipType) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));

            // Find and activate the correct tab
            const tabButtons = document.querySelectorAll('.tab-btn');
            tabButtons.forEach(btn => {
                const btnText = btn.textContent.toLowerCase();
                if (
                    (scholarshipType === 'government' && btnText.includes('government')) ||
                    (scholarshipType === 'academic' && btnText.includes('academic')) ||
                    (scholarshipType === 'employees' && btnText.includes('employee')) ||
                    (scholarshipType === 'alumni' && btnText.includes('alumni'))
                ) {
                    btn.classList.add('active');

                    // Update table title
                    const titles = {
                        'government': 'Government Grantees',
                        'academic': 'Academic Grantees',
                        'employees': 'Employee Grantees',
                        'alumni': 'Alumni Grantees'
                    };
                    document.getElementById('categoryTitle').textContent = titles[scholarshipType] ||
                        'All Grantees';

                    // Show/hide import button - only show for "All Grantees"
                    const importBtn = document.getElementById('importBtn');
                    if (importBtn) {
                        importBtn.style.display = 'none';
                    }
                }
            });
        }



        // Student management functions
        let isTabSwitching = false;

        function showStudentCategory(category, clickedButton) {
            // Prevent rapid clicking
            if (isTabSwitching) {
                return;
            }

            isTabSwitching = true;

            // Add visual feedback for the clicked button
            clickedButton.style.transform = 'scale(0.95)';
            clickedButton.style.transition = 'all 0.1s ease';

            // Remove viewing class from all tabs
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('viewing');
                btn.style.boxShadow = '';
            });

            // Add loading effect to clicked button
            setTimeout(() => {
                clickedButton.style.transform = '';
                clickedButton.classList.add('viewing');
            }, 100);

            // Build the URL with the appropriate scholarship type parameter
            let url = "{{ route('admin.students') }}";

            if (category !== 'all') {
                // Map category to scholarship type parameter
                const categoryMap = {
                    'government': 'government',
                    'academic': 'academic',
                    'employees': 'employees',
                    'alumni': 'alumni'
                };

                if (categoryMap[category]) {
                    url += '?scholarship_type=' + categoryMap[category];
                }
            }

            // Redirect to the URL with the scholarship type filter
            window.location.href = url;
        }

        // Import functions
        let selectedFile = null;

        function triggerFileImport() {
            document.getElementById('hiddenFileInput').click();
        }

        function handleFileSelection(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                document.getElementById('selectedFileName').textContent = file.name;
                document.getElementById('importConfirmationModal').style.display = 'block';
            }
        }

        function closeImportConfirmationModal() {
            document.getElementById('importConfirmationModal').style.display = 'none';
            document.getElementById('updateExistingCheckbox').checked = false;
            selectedFile = null;
            // Reset the file input
            document.getElementById('hiddenFileInput').value = '';
        }

        async function proceedWithImport() {
            const updateExisting = document.getElementById('updateExistingCheckbox').checked;

            if (!selectedFile) {
                alert('No file selected.');
                return;
            }

            const endpoint = '/admin/import-grantees-dynamic';
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('update_existing', updateExisting ? '1' : '0');

            const submitBtn = event.target;
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.textContent = 'Importing...';
            submitBtn.disabled = true;

            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                            'content')
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    let message = result.message;
                    if (result.details) {
                        message += `\n\nDetails:\n- Total imported: ${result.details.total_imported}`;
                        if (result.details.users_created) {
                            message += `\n- Users created: ${result.details.users_created}`;
                        }
                        if (result.details.grantees_created) {
                            message += `\n- Grantees created: ${result.details.grantees_created}`;
                        }
                        if (result.details.scholarship_types) {
                            message += `\n- Scholarship types found: ${result.details.scholarship_types.join(', ')}`;
                        }
                        if (result.details.errors && result.details.errors.length > 0) {
                            message += `\n\nErrors encountered:\n${result.details.errors.slice(0, 5).join('\n')}`;
                            if (result.details.errors.length > 5) {
                                message += `\n... and ${result.details.errors.length - 5} more errors`;
                            }
                        }
                    }
                    alert(message);
                    closeImportConfirmationModal();
                    // Reload the page to show new grantees
                    window.location.reload();
                } else {
                    alert(`Import failed: ${result.message}`);
                }
            } catch (error) {
                console.error('Import error:', error);
                alert('An error occurred during import. Please try again.');
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        }

        // Simple, direct course population functions
        function populateCoursesDirectly(department) {
            console.log('=== ACADEMIC COURSE POPULATION ===');
            console.log('Department selected:', department);
            console.log('Function called at:', new Date().toLocaleTimeString());

            const courseSelect = document.getElementById('course');
            console.log('Course select element:', courseSelect);

            if (!courseSelect) {
                console.error('Course select not found - this is the problem!');
                // Try to find it with a different approach
                const allSelects = document.querySelectorAll('select');
                console.log('All select elements found:', allSelects);
                allSelects.forEach((select, index) => {
                    console.log(`Select ${index}:`, select.id, select.name);
                });
                return;
            }

            console.log('Course select found, proceeding...');

            // Clear existing options
            courseSelect.innerHTML = '<option value="">Select Course</option>';
            courseSelect.disabled = true;

            if (!department) {
                console.log('No department provided, stopping');
                return;
            }

            const courses = {
                'SITE': [
                    'Bachelor of Science in Information Technology',
                    'Bachelor of Science in Computer Science',
                    'Bachelor of Science in Computer Engineering',
                    'Bachelor of Library and Information Science',
                    'Bachelor of Science in Civil Engineering',
                    'Bachelor of Science in Environmental and Sanitary Engineering'
                ],
                'SASTE': [
                    'Bachelor of Elementary Education',
                    'Bachelor of Secondary Education',
                    'Bachelor of Science in Psychology',
                    'Bachelor of Arts in English Language Studies',
                    'Bachelor of Science in Biology',
                    'Bachelor of Science in Social Work',
                    'Bachelor of Science in Public Administration',
                    'Bachelor of Physical Education'
                ],
                'SBAHM': [
                    'Bachelor of Science in Business Administration',
                    'Bachelor of Science in Hospitality Management',
                    'Bachelor of Science in Tourism Management',
                    'Bachelor of Science in Entrepreneurship'
                ],
                'SNAHS': [
                    'Bachelor of Science in Nursing',
                    'Bachelor of Science in Medical Technology',
                    'Bachelor of Science in Pharmacy',
                    'Bachelor of Science in Physical Therapy'
                ]
            };

            if (courses[department]) {
                console.log('Found courses for department:', department);
                console.log('Courses to add:', courses[department]);

                courses[department].forEach((course, index) => {
                    const option = document.createElement('option');
                    option.value = course;
                    option.textContent = course;
                    courseSelect.appendChild(option);
                    console.log(`Added course ${index + 1}:`, course);
                });

                courseSelect.disabled = false;
                console.log('Course select enabled');
                console.log('Final course select HTML:', courseSelect.innerHTML);
                console.log('Course select disabled status:', courseSelect.disabled);
                console.log('=== COURSE POPULATION COMPLETE ===');
            } else {
                console.error('No courses found for department:', department);
                console.log('Available departments:', Object.keys(courses));
            }
        }

        function populateGovernmentCoursesDirectly(department) {
            console.log('Direct course population for Government:', department);

            const courseSelect = document.getElementById('governmentCourse');
            if (!courseSelect) {
                console.error('Government Course select not found');
                return;
            }

            // Clear existing options
            courseSelect.innerHTML = '<option value="">Select Course</option>';
            courseSelect.disabled = true;

            if (!department) return;

            const courses = {
                'SITE': [
                    'Bachelor of Science in Information Technology',
                    'Bachelor of Science in Computer Science',
                    'Bachelor of Science in Computer Engineering',
                    'Bachelor of Library and Information Science',
                    'Bachelor of Science in Civil Engineering',
                    'Bachelor of Science in Environmental and Sanitary Engineering'
                ],
                'SASTE': [
                    'Bachelor of Elementary Education',
                    'Bachelor of Secondary Education',
                    'Bachelor of Science in Psychology',
                    'Bachelor of Arts in English Language Studies',
                    'Bachelor of Science in Biology',
                    'Bachelor of Science in Social Work',
                    'Bachelor of Science in Public Administration',
                    'Bachelor of Physical Education'
                ],
                'SBAHM': [
                    'Bachelor of Science in Business Administration',
                    'Bachelor of Science in Hospitality Management',
                    'Bachelor of Science in Tourism Management',
                    'Bachelor of Science in Entrepreneurship'
                ],
                'SNAHS': [
                    'Bachelor of Science in Nursing',
                    'Bachelor of Science in Medical Technology',
                    'Bachelor of Science in Pharmacy',
                    'Bachelor of Science in Physical Therapy'
                ]
            };

            if (courses[department]) {
                courses[department].forEach(course => {
                    const option = document.createElement('option');
                    option.value = course;
                    option.textContent = course;
                    courseSelect.appendChild(option);
                });
                courseSelect.disabled = false;
                console.log('Government Courses populated successfully for', department);
            }
        }

        function populateAcademicCoursesDirectly(department) {
            console.log('Direct course population for Academic:', department);

            const courseSelect = document.getElementById('academicCourse');
            if (!courseSelect) {
                console.error('Academic Course select not found');
                return;
            }

            // Clear existing options
            courseSelect.innerHTML = '<option value="">Select Course</option>';
            courseSelect.disabled = true;

            if (!department) return;

            const courses = {
                'SITE': [
                    'Bachelor of Science in Information Technology',
                    'Bachelor of Science in Computer Science',
                    'Bachelor of Science in Computer Engineering',
                    'Bachelor of Library and Information Science',
                    'Bachelor of Science in Civil Engineering',
                    'Bachelor of Science in Environmental and Sanitary Engineering'
                ],
                'SASTE': [
                    'Bachelor of Elementary Education',
                    'Bachelor of Secondary Education',
                    'Bachelor of Science in Psychology',
                    'Bachelor of Arts in English Language Studies',
                    'Bachelor of Science in Biology',
                    'Bachelor of Science in Social Work',
                    'Bachelor of Science in Public Administration',
                    'Bachelor of Physical Education'
                ],
                'SBAHM': [
                    'Bachelor of Science in Business Administration',
                    'Bachelor of Science in Hospitality Management',
                    'Bachelor of Science in Tourism Management',
                    'Bachelor of Science in Entrepreneurship'
                ],
                'SNAHS': [
                    'Bachelor of Science in Nursing',
                    'Bachelor of Science in Medical Technology',
                    'Bachelor of Science in Pharmacy',
                    'Bachelor of Science in Physical Therapy'
                ]
            };

            if (courses[department]) {
                courses[department].forEach(course => {
                    const option = document.createElement('option');
                    option.value = course;
                    option.textContent = course;
                    courseSelect.appendChild(option);
                });
                courseSelect.disabled = false;
                console.log('Academic Courses populated successfully for', department);
            }
        }





        function updateEducationStageFields() {
            const educationStage = document.getElementById('educationStage')?.value;
            const beuFields = document.getElementById('beuFields');
            const collegeFields = document.getElementById('collegeFields');

            if (!beuFields || !collegeFields) return;

            if (educationStage === 'BEU') {
                beuFields.style.display = 'block';
                collegeFields.style.display = 'none';

                // Make BEU fields required
                const gradeLevel = document.getElementById('gradeLevel');
                if (gradeLevel) gradeLevel.required = true;

                // Make college fields not required
                const department = document.getElementById('department');
                const course = document.getElementById('course');
                const yearLevel = document.getElementById('yearLevel');
                if (department) department.required = false;
                if (course) course.required = false;
                if (yearLevel) yearLevel.required = false;

            } else if (educationStage === 'College') {
                beuFields.style.display = 'none';
                collegeFields.style.display = 'block';

                // Make college fields required
                const department = document.getElementById('department');
                const course = document.getElementById('course');
                const yearLevel = document.getElementById('yearLevel');
                if (department) department.required = true;
                if (course) course.required = true;
                if (yearLevel) yearLevel.required = true;

                // Make BEU fields not required
                const gradeLevel = document.getElementById('gradeLevel');
                const strand = document.getElementById('strand');
                if (gradeLevel) gradeLevel.required = false;
                if (strand) strand.required = false;

            } else {
                beuFields.style.display = 'none';
                collegeFields.style.display = 'none';
            }
        }

        function updateEducationFields() {
            const educationStage = document.getElementById('educationStage').value;
            const specificFields = document.getElementById('educationSpecificFields');

            if (!specificFields) return;

            let fieldsHTML = '';

            if (educationStage === 'College') {
                fieldsHTML = `
                    <div class="form-row">
                        <div class="form-group">
                            <label for="department">Department *</label>
                            <select id="department" name="department" required onchange="populateCoursesDirectly(this.value); console.log('Department changed to:', this.value);">
                                <option value="">Select Department</option>
                                <option value="SITE">School of Information Technology and Engineering (SITE)</option>
                                <option value="SASTE">School of Arts, Sciences and Teacher Education (SASTE)</option>
                                <option value="SBAHM">School of Business Administration and Hospitality Management (SBAHM)</option>
                                <option value="SNAHS">School of Nursing and Allied Health Sciences (SNAHS)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="course">Course *</label>
                            <select id="course" name="course" required onchange="loadYearLevelsAndCheckSubjects()">
                                <option value="">Select Course</option>
                                <option value="Bachelor of Science in Information Technology">Bachelor of Science in Information Technology</option>
                                <option value="Bachelor of Science in Computer Science">Bachelor of Science in Computer Science</option>
                                <option value="Bachelor of Science in Computer Engineering">Bachelor of Science in Computer Engineering</option>
                                <option value="Bachelor of Library and Information Science">Bachelor of Library and Information Science</option>
                                <option value="Bachelor of Science in Civil Engineering">Bachelor of Science in Civil Engineering</option>
                                <option value="Bachelor of Science in Environmental and Sanitary Engineering">Bachelor of Science in Environmental and Sanitary Engineering</option>
                            </select>
                            <small style="color: #666; font-size: 12px;">
                                <button type="button" onclick="populateCoursesDirectly('SITE')" style="background: #1e5631; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 11px; margin-top: 5px;">
                                    🔄 Refresh Courses
                                </button>
                            </small>
                        </div>
                        <div class="form-group">
                            <label for="yearLevel">Year Level *</label>
                            <select id="yearLevel" name="year_level" required disabled onchange="checkAndShowSubjects()">
                                <option value="">Select Year Level</option>
                            </select>
                        </div>
                    </div>
                `;
            } else if (educationStage === 'BEU') {
                fieldsHTML = `
                    <div class="form-row">
                        <div class="form-group">
                            <label for="gradeLevel">Grade Level *</label>
                            <select id="gradeLevel" name="grade_level" required>
                                <option value="">Select Grade Level</option>
                                <option value="Grade 11">Grade 11</option>
                                <option value="Grade 12">Grade 12</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="strand">Strand *</label>
                            <input type="text" id="strand" name="strand" required>
                        </div>
                    </div>
                `;
            }

            specificFields.innerHTML = fieldsHTML;

            // Hide subjects section when education stage changes
            hideSubjectsSection();
        }

        // Government Education Fields Handler
        function updateGovernmentEducationFields() {
            const educationStage = document.getElementById('educationStage').value;
            const specificFields = document.getElementById('governmentEducationSpecificFields');

            if (!specificFields) return;

            let fieldsHTML = '';

            if (educationStage === 'College') {
                fieldsHTML = `
                    <div class="form-section">
                        <h3>Academic Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="governmentDepartment">Department *</label>
                                <select id="governmentDepartment" name="department" required onchange="populateGovernmentCoursesDirectly(this.value)">
                                    <option value="">Select Department</option>
                                    <option value="SITE">School of Information Technology and Engineering (SITE)</option>
                                    <option value="SASTE">School of Arts, Sciences and Teacher Education (SASTE)</option>
                                    <option value="SBAHM">School of Business Administration and Hospitality Management (SBAHM)</option>
                                    <option value="SNAHS">School of Nursing and Allied Health Sciences (SNAHS)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="governmentCourse">Course *</label>
                                <select id="governmentCourse" name="course" required disabled>
                                    <option value="">Select Course</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="governmentYearLevel">Year Level *</label>
                                <select id="governmentYearLevel" name="year_level" required>
                                    <option value="">Select Year Level</option>
                                    <option value="1st Year">1st Year</option>
                                    <option value="2nd Year">2nd Year</option>
                                    <option value="3rd Year">3rd Year</option>
                                    <option value="4th Year">4th Year</option>
                                </select>
                            </div>
                        </div>
                    </div>
                `;
            } else if (educationStage === 'BEU') {
                fieldsHTML = `
                    <div class="form-section">
                        <h3>Basic Education Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="governmentGradeLevel">Grade Level *</label>
                                <select id="governmentGradeLevel" name="grade_level" required onchange="updateGovernmentStrandField()">
                                    <option value="">Select Grade Level</option>
                                    <option value="Grade 7">Grade 7</option>
                                    <option value="Grade 8">Grade 8</option>
                                    <option value="Grade 9">Grade 9</option>
                                    <option value="Grade 10">Grade 10</option>
                                    <option value="Grade 11">Grade 11</option>
                                    <option value="Grade 12">Grade 12</option>
                                </select>
                            </div>
                            <div id="governmentStrandField"></div>
                        </div>
                    </div>
                `;
            }

            specificFields.innerHTML = fieldsHTML;
        }

        function updateGovernmentStrandField() {
            const gradeLevel = document.getElementById('governmentGradeLevel').value;
            const strandField = document.getElementById('governmentStrandField');

            if (!strandField) return;

            let strandHTML = '';

            if (gradeLevel === 'Grade 11' || gradeLevel === 'Grade 12') {
                strandHTML = `
                    <div class="form-group">
                        <label for="governmentStrand">Strand *</label>
                        <select id="governmentStrand" name="strand" required>
                            <option value="">Select Strand</option>
                            <option value="STEM">Science, Technology, Engineering and Mathematics (STEM)</option>
                            <option value="ABM">Accountancy, Business and Management (ABM)</option>
                            <option value="HUMSS">Humanities and Social Sciences (HUMSS)</option>
                        </select>
                    </div>
                `;
            }

            strandField.innerHTML = strandHTML;
        }

        async function loadGovernmentCoursesByDepartment() {
            const departmentSelect = document.getElementById('governmentDepartment');
            const courseSelect = document.getElementById('governmentCourse');

            console.log('Loading Government courses for department...');

            if (!departmentSelect || !courseSelect) {
                console.error('Department or course select not found');
                return;
            }

            const selectedDepartment = departmentSelect.value;
            console.log('Selected department:', selectedDepartment);

            // Clear existing options
            courseSelect.innerHTML = '<option value="">Select Course</option>';
            courseSelect.disabled = true;

            if (!selectedDepartment) return;

            // Use fallback courses immediately for reliability
            const fallbackCourses = {
                'SITE': [
                    'Bachelor of Science in Information Technology',
                    'Bachelor of Science in Computer Science',
                    'Bachelor of Science in Computer Engineering',
                    'Bachelor of Library and Information Science',
                    'Bachelor of Science in Civil Engineering',
                    'Bachelor of Science in Environmental and Sanitary Engineering'
                ],
                'SASTE': [
                    'Bachelor of Elementary Education',
                    'Bachelor of Secondary Education',
                    'Bachelor of Science in Psychology',
                    'Bachelor of Arts in English Language Studies',
                    'Bachelor of Science in Biology',
                    'Bachelor of Science in Social Work',
                    'Bachelor of Science in Public Administration',
                    'Bachelor of Physical Education'
                ],
                'SBAHM': [
                    'Bachelor of Science in Business Administration',
                    'Bachelor of Science in Hospitality Management',
                    'Bachelor of Science in Tourism Management',
                    'Bachelor of Science in Entrepreneurship'
                ],
                'SNAHS': [
                    'Bachelor of Science in Nursing',
                    'Bachelor of Science in Medical Technology',
                    'Bachelor of Science in Pharmacy',
                    'Bachelor of Science in Physical Therapy'
                ]
            };

            try {
                // Try to load from API first
                console.log('Attempting to fetch from API...');
                const response = await fetch('/api/scholarship/department-course-mapping');
                console.log('API response status:', response.status);

                if (response.ok) {
                    const apiData = await response.json();
                    console.log('API data received:', apiData);

                    if (apiData[selectedDepartment] && apiData[selectedDepartment].length > 0) {
                        console.log('Using API data for courses');
                        apiData[selectedDepartment].forEach(course => {
                            const option = document.createElement('option');
                            option.value = course;
                            option.textContent = course;
                            courseSelect.appendChild(option);
                        });
                        courseSelect.disabled = false;
                        return;
                    }
                }
            } catch (error) {
                console.error('API error:', error);
                // Fallback to default courses will be handled below
            }

            // Use fallback data
            console.log('Using fallback courses for department:', selectedDepartment);
            if (fallbackCourses[selectedDepartment]) {
                fallbackCourses[selectedDepartment].forEach(course => {
                    const option = document.createElement('option');
                    option.value = course;
                    option.textContent = course;
                    courseSelect.appendChild(option);
                });
                courseSelect.disabled = false;
                console.log('Courses loaded successfully from fallback');
            } else {
                console.error('No courses found for department:', selectedDepartment);
            }
        }

        // Department-Course mapping and subjects functionality
        let departmentCourses = {};

        async function loadCoursesByDepartment() {
            const departmentSelect = document.getElementById('department');
            const courseSelect = document.getElementById('course');
            const yearLevelSelect = document.getElementById('yearLevel');

            console.log('Loading Academic courses for department...');

            if (!departmentSelect || !courseSelect) {
                console.error('Department or course select not found for Academic');
                return;
            }

            const selectedDepartment = departmentSelect.value;
            console.log('Selected department for Academic:', selectedDepartment);

            // Clear existing options
            courseSelect.innerHTML = '<option value="">Select Course</option>';
            if (yearLevelSelect) {
                yearLevelSelect.innerHTML = '<option value="">Select Year Level</option>';
                yearLevelSelect.disabled = true;
            }
            courseSelect.disabled = true;
            hideSubjectsSection();

            if (!selectedDepartment) return;

            // Use fallback courses immediately for reliability
            const fallbackCourses = {
                'SITE': [
                    'Bachelor of Science in Information Technology',
                    'Bachelor of Science in Computer Science',
                    'Bachelor of Science in Computer Engineering',
                    'Bachelor of Library and Information Science',
                    'Bachelor of Science in Civil Engineering',
                    'Bachelor of Science in Environmental and Sanitary Engineering'
                ],
                'SASTE': [
                    'Bachelor of Elementary Education',
                    'Bachelor of Secondary Education',
                    'Bachelor of Science in Psychology',
                    'Bachelor of Arts in English Language Studies',
                    'Bachelor of Science in Biology',
                    'Bachelor of Science in Social Work',
                    'Bachelor of Science in Public Administration',
                    'Bachelor of Physical Education'
                ],
                'SBAHM': [
                    'Bachelor of Science in Business Administration',
                    'Bachelor of Science in Hospitality Management',
                    'Bachelor of Science in Tourism Management',
                    'Bachelor of Science in Entrepreneurship'
                ],
                'SNAHS': [
                    'Bachelor of Science in Nursing',
                    'Bachelor of Science in Medical Technology',
                    'Bachelor of Science in Pharmacy',
                    'Bachelor of Science in Physical Therapy'
                ]
            };

            try {
                // Try to load from API first
                console.log('Attempting to fetch from API for Academic...');
                const response = await fetch('/api/scholarship/department-course-mapping');
                console.log('API response status for Academic:', response.status);

                if (response.ok) {
                    const apiData = await response.json();
                    console.log('API data received for Academic:', apiData);

                    if (apiData[selectedDepartment] && apiData[selectedDepartment].length > 0) {
                        console.log('Using API data for Academic courses');
                        apiData[selectedDepartment].forEach(course => {
                            const option = document.createElement('option');
                            option.value = course;
                            option.textContent = course;
                            courseSelect.appendChild(option);
                        });
                        courseSelect.disabled = false;
                        return;
                    }
                }
            } catch (error) {
                console.error('API error for Academic:', error);
            }

            // Use fallback data
            console.log('Using fallback courses for Academic department:', selectedDepartment);
            if (fallbackCourses[selectedDepartment]) {
                fallbackCourses[selectedDepartment].forEach(course => {
                    const option = document.createElement('option');
                    option.value = course;
                    option.textContent = course;
                    courseSelect.appendChild(option);
                });
                courseSelect.disabled = false;
                console.log('Academic courses loaded successfully from fallback');
            } else {
                console.error('No courses found for Academic department:', selectedDepartment);
            }
        }

        async function loadYearLevelsAndCheckSubjects() {
            const courseSelect = document.getElementById('course');
            const yearLevelSelect = document.getElementById('yearLevel');

            if (!courseSelect || !yearLevelSelect) return;

            const selectedCourse = courseSelect.value;
            yearLevelSelect.innerHTML = '<option value="">Select Year Level</option>';
            yearLevelSelect.disabled = true;
            hideSubjectsSection();

            if (!selectedCourse) return;

            try {
                // Get course duration to determine available year levels
                const response = await fetch(`/api/scholarship/course-durations`);
                const courseDurations = await response.json();

                // Find the course duration
                let duration = 4; // Default to 4 years
                for (const [course, years] of Object.entries(courseDurations)) {
                    if (course === selectedCourse) {
                        duration = years;
                        break;
                    }
                }

                // Add year level options based on course duration
                for (let i = 1; i <= duration; i++) {
                    const option = document.createElement('option');
                    const yearText = i === 1 ? '1st Year' : i === 2 ? '2nd Year' : i === 3 ? '3rd Year' : i === 4 ?
                        '4th Year' : `${i}th Year`;
                    option.value = yearText;
                    option.textContent = yearText;
                    yearLevelSelect.appendChild(option);
                }

                // Enable year level select
                yearLevelSelect.disabled = false;
            } catch (error) {
                console.error('Error loading year levels:', error);
                // Fallback to default year levels
                ['1st Year', '2nd Year', '3rd Year', '4th Year'].forEach(year => {
                    const option = document.createElement('option');
                    option.value = year;
                    option.textContent = year;
                    yearLevelSelect.appendChild(option);
                });
                yearLevelSelect.disabled = false;
            }
        }

        function checkAndShowSubjects() {
            const courseSelect = document.getElementById('course');
            const semesterSelect = document.getElementById('semester');
            const yearLevelSelect = document.getElementById('yearLevel');

            if (!courseSelect || !semesterSelect || !yearLevelSelect) return;

            const selectedCourse = courseSelect.value;
            const selectedSemester = semesterSelect.value;
            const selectedYearLevel = yearLevelSelect.value;

            if (selectedCourse && selectedSemester && selectedYearLevel) {
                loadSubjectsFromAPI(selectedCourse, selectedYearLevel, selectedSemester);
            } else {
                hideSubjectsSection();
            }
        }

        async function loadSubjectsFromAPI(courseName, yearLevel, semester) {
            try {
                // Convert year level to number for API
                const yearLevelNumber = parseInt(yearLevel.replace(/\D/g, ''));

                const response = await fetch(
                    `/api/scholarship/subjects/${encodeURIComponent(courseName)}/${yearLevelNumber}/${encodeURIComponent(semester)}`
                );
                const data = await response.json();

                if (response.ok && data.subjects && data.subjects.length > 0) {
                    showSubjectsFromAPI(data.subjects);
                } else {
                    showNoSubjectsMessage(courseName, yearLevel, semester);
                }
            } catch (error) {
                console.error('Error loading subjects:', error);
                showNoSubjectsMessage(courseName, yearLevel, semester);
            }
        }

        function showSubjectsFromAPI(subjects) {
            const subjectsSection = document.getElementById('subjectsSection');
            const subjectsContainer = document.getElementById('subjectsContainer');

            if (!subjectsSection || !subjectsContainer) return;

            let subjectsHTML = '';

            subjects.forEach((subject, index) => {
                subjectsHTML += `
                    <div class="subject-row">
                        <div class="subject-info">
                            <span class="subject-code">${subject.code}</span>
                            <span class="subject-title">${subject.title}</span>
                            <span class="subject-units">${subject.units} units</span>
                        </div>
                        <div class="grade-input">
                            <input type="number"
                                   name="subject_grades[${subject.code}]"
                                   placeholder="Grade"
                                   min="1.0"
                                   max="4.0"
                                   step="0.01"
                                   onchange="calculateGWA(); validateAcademicGrades();"
                                   oninput="validateIndividualGrade(this)"
                                   data-units="${subject.units}">
                        </div>
                    </div>
                `;
            });

            subjectsContainer.innerHTML = subjectsHTML;
            subjectsSection.style.display = 'block';
        }

        function showNoSubjectsMessage(courseName, yearLevel, semester) {
            const subjectsSection = document.getElementById('subjectsSection');
            const subjectsContainer = document.getElementById('subjectsContainer');

            if (!subjectsSection || !subjectsContainer) return;

            subjectsContainer.innerHTML = `
                <div class="no-subjects-message">
                    <p>No subjects found for ${courseName} - ${yearLevel} - ${semester}</p>
                    <p>Please contact the administrator to add subjects for this course.</p>
                </div>
            `;
            subjectsSection.style.display = 'block';
        }

        function hideSubjectsSection() {
            const subjectsSection = document.getElementById('subjectsSection');
            if (subjectsSection) {
                subjectsSection.style.display = 'none';
            }
        }

        function calculateGWA() {
            const gradeInputs = document.querySelectorAll('input[name^="subject_grades"]');
            const gwaInput = document.getElementById('calculatedGwa');

            if (!gwaInput) return;

            let totalGradePoints = 0;
            let totalUnits = 0;
            let hasAllGrades = true;
            let hasDisqualifyingGrade = false;

            gradeInputs.forEach(input => {
                const grade = parseFloat(input.value);
                const units = parseFloat(input.dataset.units);

                if (!isNaN(grade) && grade > 0) {
                    totalGradePoints += grade * units;
                    totalUnits += units;

                    // Check for disqualifying grade (2.0 and above)
                    if (grade >= 2.0) {
                        hasDisqualifyingGrade = true;
                    }
                } else {
                    hasAllGrades = false;
                }
            });

            if (hasAllGrades && totalUnits > 0) {
                const gwa = totalGradePoints / totalUnits;
                gwaInput.value = gwa.toFixed(2);
            } else {
                gwaInput.value = '';
            }

            // Check for disqualifying grades and show notification
            if (hasDisqualifyingGrade) {
                showGradeDisqualificationNotification();
            } else {
                removeGradeDisqualificationNotification();
            }
        }

        // Show grade disqualification notification
        function showGradeDisqualificationNotification() {
            // Remove any existing grade disqualification notifications
            removeGradeDisqualificationNotification();

            // Create grade disqualification notification
            const notification = document.createElement('div');
            notification.className = 'main-screen-grade-disqualification-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>
                    </div>
                    <div class="notification-text">
                        <strong>Academic Scholarship Disqualification!</strong><br>
                        You have entered one or more grades of 2.0 or below, which disqualifies this student from the Academic Scholarship program.<br>
                        <span class="warning-note">Academic Scholarship requires all grades to be between 1.0 and 1.75 (passing grades). Grades of 2.0 and below are not eligible. Please review the grades and ensure they meet the requirements.</span>
                    </div>
                    <button class="notification-close" onclick="removeGradeDisqualificationNotification()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // Insert notification at the top of the modal content
            const modalContent = document.querySelector('.modal-content') || document.querySelector('.container') ||
                document.body;
            modalContent.insertBefore(notification, modalContent.firstChild);

            // Auto-scroll to show the notification
            notification.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Mark form as having disqualifying grades
            const academicForm = document.querySelector('form input[value="academic"]');
            if (academicForm) {
                academicForm.closest('form').setAttribute('data-grade-disqualified', 'true');
            }
        }

        // Remove grade disqualification notification
        function removeGradeDisqualificationNotification() {
            const existingNotification = document.querySelector('.main-screen-grade-disqualification-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // Remove disqualification flag from form
            const academicForm = document.querySelector('form input[value="academic"]');
            if (academicForm) {
                academicForm.closest('form').removeAttribute('data-grade-disqualified');
            }
        }

        // Validate individual grade input
        function validateIndividualGrade(input) {
            const grade = parseFloat(input.value);

            // Remove any existing grade error styling
            input.classList.remove('grade-disqualified');

            // If grade is entered and is 2.0 or above, mark as disqualified
            if (!isNaN(grade) && grade > 0 && grade >= 2.0) {
                input.classList.add('grade-disqualified');
            }
        }

        // Validate all academic grades
        function validateAcademicGrades() {
            const gradeInputs = document.querySelectorAll('input[name^="subject_grades"]');
            let hasDisqualifyingGrade = false;

            gradeInputs.forEach(input => {
                const grade = parseFloat(input.value);
                if (!isNaN(grade) && grade > 0 && grade >= 2.0) {
                    hasDisqualifyingGrade = true;
                }
            });

            // Show or hide disqualification notification
            if (hasDisqualifyingGrade) {
                showGradeDisqualificationNotification();
            } else {
                removeGradeDisqualificationNotification();
            }
        }

        function validateDynamicFormData(studentData) {
            // Common required fields for all scholarship types
            const commonFields = [
                { key: 'student_id', name: 'Student ID' },
                { key: 'first_name', name: 'First Name' },
                { key: 'last_name', name: 'Last Name' },
                { key: 'scholarship_type', name: 'Scholarship Type' },
                { key: 'sex', name: 'Sex' },
                { key: 'birthdate', name: 'Birthdate' },
                { key: 'contact_number', name: 'Contact Number' },
                { key: 'street', name: 'Street' },
                { key: 'barangay', name: 'Barangay' },
                { key: 'city', name: 'City' },
                { key: 'province', name: 'Province' },
                { key: 'zipcode', name: 'Zipcode' }
            ];

            // Check common required fields
            for (const field of commonFields) {
                if (!studentData[field.key] || !studentData[field.key].trim()) {
                    alert(`Please fill in the ${field.name} field.`);
                    return false;
                }
            }

            // Scholarship-specific validation
            const scholarshipType = studentData.scholarship_type;

            if (scholarshipType === 'government') {
                const governmentFields = [
                    { key: 'government_benefactor_type', name: 'Benefactor Type' },
                    { key: 'education_stage', name: 'Education Stage' },
                    { key: 'father_first_name', name: 'Father\'s First Name' },
                    { key: 'father_middle_name', name: 'Father\'s Middle Name' },
                    { key: 'father_last_name', name: 'Father\'s Last Name' },
                    { key: 'mother_first_name', name: 'Mother\'s First Name' },
                    { key: 'mother_middle_name', name: 'Mother\'s Middle Name' },
                    { key: 'mother_last_name', name: 'Mother\'s Last Name' }
                ];

                for (const field of governmentFields) {
                    if (!studentData[field.key] || !studentData[field.key].trim()) {
                        alert(`Please fill in the ${field.name} field.`);
                        return false;
                    }
                }

                // Education stage specific validation
                if (studentData.education_stage === 'BEU') {
                    if (!studentData.grade_level || !studentData.grade_level.trim()) {
                        alert('Please select a Grade Level for BEU students.');
                        return false;
                    }
                } else if (studentData.education_stage === 'College') {
                    const collegeFields = [
                        { key: 'department', name: 'Department' },
                        { key: 'course', name: 'Course' },
                        { key: 'year_level', name: 'Year Level' }
                    ];

                    for (const field of collegeFields) {
                        if (!studentData[field.key] || !studentData[field.key].trim()) {
                            alert(`Please fill in the ${field.name} field.`);
                            return false;
                        }
                    }
                }

            } else if (scholarshipType === 'academic') {
                const academicFields = [
                    { key: 'department', name: 'Department' },
                    { key: 'course', name: 'Course' },
                    { key: 'year_level', name: 'Year Level' },
                    { key: 'gwa', name: 'GWA' }
                ];

                for (const field of academicFields) {
                    if (!studentData[field.key] || !studentData[field.key].trim()) {
                        alert(`Please fill in the ${field.name} field.`);
                        return false;
                    }
                }

            } else if (scholarshipType === 'employees') {
                const employeeFields = [
                    { key: 'employee_name', name: 'Employee Name' },
                    { key: 'employee_relationship', name: 'Employee Relationship' },
                    { key: 'employee_department', name: 'Employee Department' },
                    { key: 'employee_position', name: 'Employee Position' }
                ];

                for (const field of employeeFields) {
                    if (!studentData[field.key] || !studentData[field.key].trim()) {
                        alert(`Please fill in the ${field.name} field.`);
                        return false;
                    }
                }

            } else if (scholarshipType === 'alumni') {
                if (!studentData.scholarship_name || !studentData.scholarship_name.trim()) {
                    alert('Please fill in the Scholarship Name field.');
                    return false;
                }
            }

            return true;
        }



        function editStudent(applicationId, studentId) {
            // Find the grantee data from the table row
            const button = event.target.closest('button');
            const row = button.closest('tr');

            // Get grantee data from the row
            const cells = row.querySelectorAll('td');
            const granteeId = cells[0].textContent.trim();
            const fullName = cells[1].textContent.trim();

            // Get status from the status badge
            const statusBadge = row.querySelector('.status-badge');
            const status = statusBadge ? statusBadge.textContent.trim() : 'Active';

            // Populate modal with grantee data
            document.getElementById('editStudentId').value = granteeId;
            document.getElementById('editStudentName').value = fullName;
            document.getElementById('editStudentStatus').value = status;
            document.getElementById('editStudentRemarks').value = '';
            document.getElementById('editApplicationId').value = applicationId;
            document.getElementById('editGranteeId').value = granteeId;

            // Show/hide remarks field based on status
            toggleRemarksField();

            // Show modal
            document.getElementById('editStudentModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editStudentModal').style.display = 'none';
        }

        function toggleRemarksField() {
            const statusSelect = document.getElementById('editStudentStatus');
            const remarksRow = document.getElementById('remarksRow');
            const remarksSelect = document.getElementById('editStudentRemarks');

            if (statusSelect.value === 'Inactive') {
                remarksRow.style.display = 'block';
                remarksSelect.required = true;
            } else {
                remarksRow.style.display = 'none';
                remarksSelect.required = false;
                remarksSelect.value = '';
            }
        }

        // Add event listener for status change
        document.addEventListener('DOMContentLoaded', function() {
            const statusSelect = document.getElementById('editStudentStatus');
            if (statusSelect) {
                statusSelect.addEventListener('change', toggleRemarksField);
            }
        });

        function saveStudentChanges(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const applicationId = formData.get('application_id');
            const granteeId = formData.get('grantee_id');

            // Validate required fields
            if (formData.get('status') === 'Inactive' && !formData.get('notes')) {
                alert('Please select a reason when setting status to Inactive.');
                return;
            }

            // Show loading state
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Saving...';
            submitBtn.disabled = true;

            // Send update request
            fetch(`/admin/grantees/${granteeId}/update`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(Object.fromEntries(formData))
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        closeEditModal();
                        // Simply reload to show updated data (grantee stays in the list)
                        window.location.reload();
                    } else {
                        alert(data.message || 'Failed to update grantee information.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating grantee information.');
                })
                .finally(() => {
                    // Reset button state
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
        }

        function updateTableRow(formData) {
            const studentId = formData.get('student_id');
            const rows = document.querySelectorAll('#studentsTableBody tr');

            rows.forEach(row => {
                const firstCell = row.querySelector('td');
                if (firstCell && firstCell.textContent.trim() === studentId) {
                    // Update the row data
                    const cells = row.querySelectorAll('td');
                    cells[1].textContent = formData.get('name'); // Name
                    cells[2].textContent = formData.get('course'); // Course
                    cells[5].textContent = formData.get('gwa') || 'N/A'; // GWA

                    // Update data attributes on the edit button
                    const editBtn = row.querySelector('.action-btn.edit');
                    editBtn.dataset.studentName = formData.get('name');
                    editBtn.dataset.studentEmail = formData.get('email');
                    editBtn.dataset.studentContact = formData.get('contact_number');
                    editBtn.dataset.studentCourse = formData.get('course');
                    editBtn.dataset.studentDepartment = formData.get('department');
                    editBtn.dataset.studentYear = formData.get('year_level');
                    editBtn.dataset.studentGwa = formData.get('gwa');
                }
            });
        }



        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const editModal = document.getElementById('editStudentModal');
            // Temporarily commented out Add Grantee modal handling
            // const addModal = document.getElementById('addStudentModal');

            if (event.target === editModal) {
                closeEditModal();
            }
            // Temporarily commented out Add Grantee modal handling
            // else if (event.target === addModal) {
            //     closeAddStudentModal();
            // }
        }

        function toggleDropdown(event) {
            event.preventDefault();
            const dropdown = event.currentTarget.parentElement;
            const menu = dropdown.querySelector('.dropdown-menu');
            const arrow = dropdown.querySelector('.dropdown-arrow');

            dropdown.classList.toggle('open');

            if (dropdown.classList.contains('open')) {
                menu.style.maxHeight = menu.scrollHeight + 'px';
                arrow.style.transform = 'rotate(180deg)';
            } else {
                menu.style.maxHeight = '0';
                arrow.style.transform = 'rotate(0deg)';
            }
        }
    </script>
@endpush

@push('styles')
    <style>
        .import-options {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .import-option {
            margin-bottom: 15px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .import-option:hover {
            border-color: #1e5631;
            background: #f0f8f0;
        }

        .import-option input[type="radio"] {
            margin-right: 10px;
        }

        .import-option input[type="radio"]:checked + label {
            color: #1e5631;
            font-weight: bold;
        }

        .import-option label {
            cursor: pointer;
            margin: 0;
        }

        .import-option label strong {
            display: block;
            margin-bottom: 5px;
            font-size: 16px;
        }

        .import-option label p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        .import-info {
            margin-top: 15px;
            padding: 15px;
            background: #e8f5e8;
            border-left: 4px solid #1e5631;
            border-radius: 4px;
        }

        .import-info p {
            margin: 0 0 10px 0;
            font-weight: bold;
            color: #1e5631;
        }

        .import-info ul {
            margin: 0;
            padding-left: 20px;
        }

        .import-info li {
            margin-bottom: 5px;
            color: #333;
        }

        .import-info a {
            color: #1e5631;
            text-decoration: none;
            font-weight: bold;
        }

        .import-info a:hover {
            text-decoration: underline;
        }
    </style>
@endpush
