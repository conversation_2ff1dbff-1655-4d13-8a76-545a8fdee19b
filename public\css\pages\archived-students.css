/* Archived Students Page Styles */

/* Student Categories - matching grantees tab exactly */
.student-categories {
    background-color: white;
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 30px;
}

.category-tabs {
    display: flex;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.tab-group {
    display: flex;
    gap: 5px;
}

.tab-btn {
    padding: 12px 20px;
    border: 2px solid #052F11;
    background-color: white;
    color: #052F11;
    cursor: pointer;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tab-btn:hover {
    background-color: #052F11;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(5, 47, 17, 0.3);
}

.tab-btn.active {
    background-color: #052F11;
    color: white;
    box-shadow: 0 0 15px rgba(5, 47, 17, 0.4);
    border: 2px solid rgba(5, 47, 17, 0.8);
    font-weight: 600;
}

/* Add a subtle glow effect when tab is being viewed */
.tab-btn.viewing {
    box-shadow: 0 0 10px rgba(5, 47, 17, 0.3);
    border-color: rgba(5, 47, 17, 0.7);
}

/* Enhanced hover animations */
.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tab-btn:hover::before {
    left: 100%;
}

.archive-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Remarks badge styling */
.remarks-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.remarks-badge:hover {
    background: #e9ecef;
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    max-width: none;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Export button styling for archives */
.export-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

/* ===================================
   INLINE STYLES MOVED FROM ARCHIVED STUDENTS BLADE FILE
   =================================== */

/* Enhanced Remarks Badge Styling */
.remarks-badge.inactive-remarks {
    background-color: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.remarks-badge.masterlist-remarks {
    background-color: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* Modal Styling */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: #1e5631;
    font-size: 24px;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    transition: color 0.3s;
}

.close:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
}

.loading, .error {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading i, .error i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.error {
    color: #dc3545;
}

.student-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.detail-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.detail-section h3 {
    margin: 0 0 16px 0;
    color: #1e5631;
    font-size: 18px;
    border-bottom: 2px solid #1e5631;
    padding-bottom: 8px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    font-weight: 600;
    color: #495057;
    flex: 0 0 40%;
}

.detail-row .value {
    flex: 1;
    text-align: right;
    color: #212529;
}

.detail-row .value.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.detail-row .value.badge.masterlist {
    background-color: #28a745;
    color: white;
}

.detail-row .value.badge.inactive {
    background-color: #dc3545;
    color: white;
}

.detail-row .value.remarks.inactive {
    color: #dc3545;
    font-weight: 500;
}

.detail-row .value.remarks.masterlist {
    color: #28a745;
    font-weight: 500;
}

/* Responsive Design for Modal */
@media (max-width: 768px) {
    .student-details-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .detail-row .value {
        text-align: left;
    }
}

/* Hidden elements by default */
.table-header.hidden {
    display: none;
}

#archivedStudentModal {
    display: none;
}
