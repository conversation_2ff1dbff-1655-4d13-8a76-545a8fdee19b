<?php

namespace App\Helpers;

class ScholarshipDataHelper
{
    /**
     * Get all departments with their codes and names
     */
    public static function getDepartments()
    {
        return [
            'SITE' => 'School of Information Technology and Engineering',
            'SBA' => 'School of Business Administration',
            'SHANS' => 'School of Health and Natural Sciences',
            'SHSS' => 'School of Humanities and Social Sciences',
            'STE' => 'School of Teacher Education'
        ];
    }

    /**
     * Get courses by department
     */
    public static function getCoursesByDepartment($departmentCode = null)
    {
        $courses = [
            'SITE' => [
                'BSIT' => 'Bachelor of Science in Information Technology',
                'BSCS' => 'Bachelor of Science in Computer Science',
                'BSCpE' => 'Bachelor of Science in Computer Engineering',
                'BSECE' => 'Bachelor of Science in Electronics and Communications Engineering',
                'BSEE' => 'Bachelor of Science in Electrical Engineering',
                'BSCE' => 'Bachelor of Science in Civil Engineering',
                'BSME' => 'Bachelor of Science in Mechanical Engineering'
            ],
            'SBA' => [
                'BSBA-MM' => 'Bachelor of Science in Business Administration Major in Marketing Management',
                'BSBA-FM' => 'Bachelor of Science in Business Administration Major in Financial Management',
                'BSBA-HRM' => 'Bachelor of Science in Business Administration Major in Human Resource Management',
                'BSA' => 'Bachelor of Science in Accountancy',
                'BSMA' => 'Bachelor of Science in Management Accounting',
                'BSTM' => 'Bachelor of Science in Tourism Management',
                'BSHM' => 'Bachelor of Science in Hospitality Management'
            ],
            'SHANS' => [
                'BSN' => 'Bachelor of Science in Nursing',
                'BSMT' => 'Bachelor of Science in Medical Technology',
                'BSPT' => 'Bachelor of Science in Physical Therapy',
                'BSRT' => 'Bachelor of Science in Radiologic Technology',
                'BSPsych' => 'Bachelor of Science in Psychology',
                'BSBio' => 'Bachelor of Science in Biology',
                'BSChem' => 'Bachelor of Science in Chemistry',
                'BSMath' => 'Bachelor of Science in Mathematics'
            ],
            'SHSS' => [
                'AB-Eng' => 'Bachelor of Arts in English',
                'AB-Pol' => 'Bachelor of Arts in Political Science',
                'AB-Hist' => 'Bachelor of Arts in History',
                'AB-Phil' => 'Bachelor of Arts in Philosophy',
                'BSDC' => 'Bachelor of Science in Development Communication',
                'BSS' => 'Bachelor of Science in Sociology'
            ],
            'STE' => [
                'BEEd' => 'Bachelor of Elementary Education',
                'BSEd-Eng' => 'Bachelor of Secondary Education Major in English',
                'BSEd-Math' => 'Bachelor of Secondary Education Major in Mathematics',
                'BSEd-Sci' => 'Bachelor of Secondary Education Major in Science',
                'BSEd-SS' => 'Bachelor of Secondary Education Major in Social Studies',
                'BSEd-Fil' => 'Bachelor of Secondary Education Major in Filipino',
                'BPED' => 'Bachelor of Physical Education'
            ]
        ];

        if ($departmentCode && isset($courses[$departmentCode])) {
            return $courses[$departmentCode];
        }

        return $departmentCode ? [] : $courses;
    }

    /**
     * Get all courses (flattened)
     */
    public static function getAllCourses()
    {
        $allCourses = [];
        $coursesByDept = self::getCoursesByDepartment();
        
        foreach ($coursesByDept as $courses) {
            $allCourses = array_merge($allCourses, $courses);
        }
        
        return $allCourses;
    }

    /**
     * Get department by course code
     */
    public static function getDepartmentByCourse($courseCode)
    {
        $coursesByDept = self::getCoursesByDepartment();
        
        foreach ($coursesByDept as $deptCode => $courses) {
            if (array_key_exists($courseCode, $courses)) {
                return $deptCode;
            }
        }
        
        return null;
    }

    /**
     * Get BEU strands for Basic Education Unit
     */
    public static function getBEUStrands()
    {
        return [
            'STEM' => 'Science, Technology, Engineering and Mathematics',
            'ABM' => 'Accountancy, Business and Management',
            'HUMSS' => 'Humanities and Social Sciences',
            'GAS' => 'General Academic Strand',
            'TVL-ICT' => 'Technical-Vocational-Livelihood - Information and Communications Technology',
            'TVL-HE' => 'Technical-Vocational-Livelihood - Home Economics',
            'TVL-IA' => 'Technical-Vocational-Livelihood - Industrial Arts'
        ];
    }

    /**
     * Get year levels for college
     */
    public static function getCollegeYearLevels()
    {
        return [
            '1st Year',
            '2nd Year', 
            '3rd Year',
            '4th Year',
            '5th Year'
        ];
    }

    /**
     * Get grade levels for BEU
     */
    public static function getBEUGradeLevels()
    {
        return [
            'Grade 7',
            'Grade 8',
            'Grade 9',
            'Grade 10',
            'Grade 11',
            'Grade 12'
        ];
    }

    /**
     * Get semesters
     */
    public static function getSemesters()
    {
        return [
            '1st Semester',
            '2nd Semester',
            'Summer'
        ];
    }

    /**
     * Get scholarship types
     */
    public static function getScholarshipTypes()
    {
        return [
            'government' => 'Government Scholarship',
            'academic' => 'Academic Scholarship', 
            'employees' => 'Employee Scholarship',
            'alumni' => 'Alumni Scholarship'
        ];
    }

    /**
     * Get government benefactor types
     */
    public static function getGovernmentBenefactorTypes()
    {
        return [
            'CHED' => 'Commission on Higher Education',
            'DOST' => 'Department of Science and Technology',
            'DSWD' => 'Department of Social Welfare and Development',
            'DOLE' => 'Department of Labor and Employment'
        ];
    }

    /**
     * Get academic scholarship subtypes
     */
    public static function getAcademicScholarshipSubtypes()
    {
        return [
            'PL' => "President's Lister (GWA 1.0-1.25)",
            'DL' => "Dean's Lister (GWA 1.26-1.74)"
        ];
    }

    /**
     * Get employee relationships
     */
    public static function getEmployeeRelationships()
    {
        return [
            'Son',
            'Daughter', 
            'Spouse'
        ];
    }
}
