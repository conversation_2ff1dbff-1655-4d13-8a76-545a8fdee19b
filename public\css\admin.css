/* Admin Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
}

/* University Header */
.university-header {
    background-color: #052F11;
    color: white;
    padding: 15px 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.university-logo-title {
    display: flex;
    align-items: center;
}

.university-logo {
    height: 60px;
    margin-right: 15px;
}

.university-title h1 {
    font-size: 20px;
    font-weight: 700;
    margin: 0;
}

.university-title h2 {
    font-size: 14px;
    font-weight: 400;
    margin: 5px 0 0;
}

.user-actions {
    display: flex;
    align-items: center;
}

.logout-btn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logout-btn i {
    margin-right: 5px;
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Dashboard Banner */
.dashboard-banner {
    background-color: #f0f0f0;
    padding: 15px 0;
    border-bottom: 1px solid #ddd;
}

.banner-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.dashboard-banner h2 {
    font-size: 16px;
    color: #052F11;
    margin: 0;
}

@media (max-width: 768px) {
    .banner-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* Admin Container */
.admin-container {
    display: flex;
    max-width: 1400px;
    margin: 20px auto;
    padding: 0 20px;
}

/* Sidebar */
.admin-sidebar {
    width: 250px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-right: 20px;
    flex-shrink: 0;
}

.admin-profile {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.profile-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.profile-image i {
    font-size: 24px;
    color: #1e5631;
}

.profile-info h3 {
    font-size: 16px;
    margin: 0 0 5px;
}

.profile-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.admin-nav {
    display: flex;
    flex-direction: column;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    border-radius: 5px;
    margin-bottom: 5px;
    transition: background-color 0.3s ease;
}

.nav-item i {
    margin-right: 10px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.nav-item:hover {
    background-color: #f5f5f5;
}

.nav-item.active {
    background-color: #e6f7e9;
    color: #1e5631;
    font-weight: 500;
}

/* Main Content */
.admin-content {
    flex-grow: 1;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.dashboard-header h1 {
    font-size: 24px;
    color: #333;
}

.date-filter {
    font-size: 14px;
    color: #666;
}



/* Responsive Design */
@media (max-width: 1024px) {
    .admin-container {
        flex-direction: column;
    }

    .admin-sidebar {
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }

    .admin-profile {
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

    .admin-nav {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .nav-item {
        margin-right: 10px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .university-logo-title {
        margin-bottom: 15px;
    }

    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .dashboard-header h1 {
        margin-bottom: 10px;
    }

    .admin-nav {
        flex-direction: column;
    }

    .nav-item {
        margin-right: 0;
    }
}

/* Table Styles */
.table-container {
    overflow-x: auto;
}

.applications-table {
    width: 100%;
    border-collapse: collapse;
}

.applications-table th {
    text-align: left;
    padding: 12px 15px;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #eee;
}

.applications-table td {
    padding: 12px 15px;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #eee;
}

.applications-table tr:hover {
    background-color: #f5f5f5;
}

/* Pagination */
.pagination-container {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
    gap: 5px;
}

.pagination li {
    margin: 0;
}

.pagination li a,
.pagination li span {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border-radius: 8px;
    text-decoration: none;
    color: #1e5631;
    background-color: white;
    border: 1px solid #e0e0e0;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.pagination li.active span {
    background-color: #1e5631;
    color: white;
    border-color: #1e5631;
    box-shadow: 0 2px 4px rgba(30, 86, 49, 0.2);
}

.pagination li a:hover {
    background-color: #f8f9fa;
    border-color: #1e5631;
    color: #1e5631;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination li.disabled span {
    color: #ccc;
    background-color: #f8f9fa;
    border-color: #e0e0e0;
    cursor: not-allowed;
}

/* Previous/Next buttons */
.pagination li:first-child a,
.pagination li:last-child a {
    padding: 0 16px;
}

.pagination .page-link {
    border: 1px solid #e0e0e0;
}

/* Pagination wrapper */
.pagination-wrapper {
    display: flex;
    justify-content: center;
}

/* Hide default Laravel pagination text */
.pagination-wrapper .hidden {
    display: none;
}

/* Improve pagination dots */
.pagination li span:contains("...") {
    border: none;
    background: none;
    color: #999;
    cursor: default;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .pagination li a,
    .pagination li span {
        min-width: 35px;
        height: 35px;
        font-size: 13px;
    }

    .pagination-info {
        font-size: 12px;
    }
}

/* Filter Controls */
.filter-controls {
    display: flex;
    gap: 10px;
}

.filter-select {
    padding: 8px 12px;
    border-radius: 5px;
    border: 1px solid #ddd;
    font-size: 14px;
    color: #333;
}

/* Status Badges */
.status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.status.pending {
    background-color: #fff8e1;
    color: #f39c12;
}

.status.review {
    background-color: #e3f2fd;
    color: #2196f3;
}

.status.approved {
    background-color: #e6f7e9;
    color: #052F11;
}

.status.rejected {
    background-color: #fde8e8;
    color: #e74c3c;
}

/* Action Buttons */
.action-btn {
    display: inline-block;
    padding: 5px 10px;
    background-color: #052F11;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.action-btn:hover {
    background-color: #052F11;
}

/* Dropdown Menu Styles */
.nav-item.dropdown {
    position: relative;
}

.nav-item.dropdown .nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    color: #b0bec5;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 10px;
}

.nav-item.dropdown .nav-link:hover,
.nav-item.dropdown.open .nav-link {
    background: #052f11;
    color: white;
}

.dropdown-arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.dropdown-menu {
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin: 5px 10px;
}

.dropdown-menu.active {
    max-height: 200px;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 20px;
    color: #90a4ae;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 2px 8px;
    font-size: 14px;
}

.dropdown-item:hover {
    background: #052f11;
    color: white;
    transform: translateX(5px);
}

.dropdown-item.active {
    background: #052f11;
    color: white;
}

.dropdown-item i {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

/* Filter Info Styles */
.filter-info {
    margin-bottom: 20px;
}

.filter-badge {
    background: #052f11;
    border: 1px solid #052f11;
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    color: white;
    gap: 10px;
    color: #0277bd;
    font-size: 14px;
    font-weight: 500;
}

.filter-badge i {
    color: #0277bd;
}

.clear-filter {
    margin-left: auto;
    color: #0277bd;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.clear-filter:hover {
    background: rgba(2, 119, 189, 0.1);
    color: #01579b;
    text-decoration: none;
}

/* Semester and Academic Year Badges */
.semester-badge {
    display: inline-block;
    padding: 4px 8px;
    background: #052f11;
    color: white;
    border-radius: 8px;
    font-weight: 600;
    font-size: 11px;
    text-align: center;
    border: 1px solid #052f11;
}

.academic-year-badge {
    display: inline-block;
    padding: 4px 8px;
    background: #052f11;
    color: white;
    border-radius: 8px;
    font-weight: 600;
    font-size: 11px;
    text-align: center;
    border: 1px solid #052f11;
}

/* Breadcrumb Styles */
.breadcrumb-nav {
    margin-bottom: 20px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #052F11;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.breadcrumb-link:hover {
    background: rgba(5, 47, 17, 0.1);
    color: #1e5631;
}

.breadcrumb-item.active {
    color: #6c757d;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: #6c757d;
    font-size: 12px;
}

/* Simple Notification Styles */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.notification {
    max-width: 300px;
    padding: 10px 14px;
    margin-bottom: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 13px;
    border: 1px solid;
}

.notification.success {
    background-color: #f0f9f0;
    border-color: #28a745;
    color: #155724;
}

.notification.error {
    background-color: #fdf2f2;
    border-color: #dc3545;
    color: #721c24;
}

.notification.warning {
    background-color: #fffbf0;
    border-color: #ffc107;
    color: #856404;
}

.notification.info {
    background-color: #f0f8ff;
    border-color: #17a2b8;
    color: #0c5460;
}

.notification .close-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.7;
    margin-left: 8px;
    padding: 0;
    width: 20px;
    height: 20px;
}

.notification .close-btn:hover {
    opacity: 1;
}

/* Legacy notification styles for backward compatibility */
.success-message, .alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.error-message, .alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Update Semester/Year Modal Styles */
.update-selection h4 {
    margin-bottom: 20px;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
}

.update-options {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.update-option-card {
    flex: 1;
    min-width: 280px;
    padding: 20px;
    border: 2px solid #dee2e6;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    position: relative;
}

.update-option-card:hover {
    border-color: #052F11;
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(5, 47, 17, 0.1);
}

.update-option-card.selected {
    border-color: #052F11;
    background: #f8f9fa;
    box-shadow: 0 6px 20px rgba(5, 47, 17, 0.15);
}

.option-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: #052f11;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.option-content {
    flex: 1;
}

.option-content h3 {
    margin: 0 0 8px 0;
    color: #052F11;
    font-size: 18px;
    font-weight: 600;
}

.option-content p {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
    line-height: 1.4;
}

.option-content small {
    display: block;
    color: #dc3545;
    font-weight: 500;
    font-size: 12px;
}

.option-radio {
    position: absolute;
    top: 15px;
    right: 15px;
}

.option-radio input[type="radio"] {
    width: 20px;
    height: 20px;
    accent-color: #052F11;
    cursor: pointer;
}

/* Modal footer buttons */
.modal-footer .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-primary {
    background: #052f11;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #041f0c;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(5, 47, 17, 0.3);
}

.btn-primary:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Responsive design for update modal */
@media (max-width: 768px) {
    .update-options {
        flex-direction: column;
    }

    .update-option-card {
        min-width: auto;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Clean Table Styling with Logo Colors */

/* Basic table structure */
table {
    width: 100%;
    border-collapse: collapse;
}

table th,
table td {
    border-right: 1px solid #dee2e6;
    padding: 12px 15px;
    vertical-align: middle;
    text-align: left;
}

table th:last-child,
table td:last-child {
    border-right: none;
}

table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #dee2e6;
}

table td {
    color: #333;
    border-bottom: 1px solid #eee;
}

/* Status Badges - Logo Color Scheme */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.approved {
    background-color: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
}

.status-badge.rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.active {
    background-color: rgba(5, 47, 17, 0.1);
    color: #052F11;
    border: 1px solid rgba(5, 47, 17, 0.3);
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Action Buttons - Logo Color Scheme */
.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    margin: 0 2px;
    border: 1px solid #052F11;
    background-color: #052F11;
    color: white;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: rgba(5, 47, 17, 0.8);
    color: white;
    text-decoration: none;
}

.action-btn.view {
    background-color: #052F11;
    border-color: #052F11;
}

.action-btn.edit {
    background-color: rgba(5, 47, 17, 0.7);
    border-color: rgba(5, 47, 17, 0.7);
}

.action-btn.delete {
    background-color: #dc3545;
    border-color: #dc3545;
}

.action-btn.delete:hover {
    background-color: #c82333;
    border-color: #c82333;
}


